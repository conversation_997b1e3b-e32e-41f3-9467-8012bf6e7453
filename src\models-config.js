/**
 * 模型ID映射配置文件
 *
 * 这个文件包含了所有支持的模型名称到其内部ID的映射关系。
 *
 * 获取方式：

 */
export const modelsAIds = {
    "gpt-4.1": "14e9311c-94d2-40c2-8c54-273947e208b0",
    "claude-sonnet-4": "ac44dd10-0666-451c-b824-386ccfea7bcc",
    "gemini-2.5-flash": "ce2092c1-28d4-4d42-a1e0-6b061dfe0b20",
    "o3": "cb0f1e24-e8e9-4745-aabc-b926ffde7475",
    "o3-pro": "cb0f1e24-e8e9-4745-aabc-b926ffde7475",
    "o4-mini-high": "f1102bbf-34ca-468f-a9fc-14bcf63f315b",
    "claude-3-7-sonnet-thinking": "be98fcfd-345c-4ae1-9a82-a19123ebf1d2",
    "claude-3-7-sonnet": "c5a11495-081a-4dc6-8d9a-64a4fd6f7bbc",
    "claude-sonnet-4-thinking": "4653dded-a46b-442a-a8fe-9bb9730e2453",
    "claude-opus-4": "ee116d12-64d6-48a8-88e5-b2d06325cdd2",
    "gemini-2.5-pro": "e2d9d353-6dbe-4414-bf87-bd289d523726",
    "deep-seek-v3": "2f5253e4-75be-473c-bcfc-baeb3df0f8ad",
    "deep-seek-r1": "30ab90f5-e020-4f83-aff5-f750d2e78769",
    "kimi-k2-instruct": "7a3626fc-4e64-4c9e-821f-b449a4b43b6a",
    "groq-kimi-k2-instruct": "7a3626fc-4e64-4c9e-821f-b449a4b43b6a",
    "grok-4-0709": "b9edb8e9-4e98-49e7-8aaf-ae67e9797a11"
};

/**
 * 模型UI配置数组
 *
 * 这个数组包含了前端显示所需的模型配置信息，包括图标、标签、描述等。
 */
export const modelUIConfigs = [
    {
        name: "gpt-4.1,claude-sonnet-4,gemini-2.5-flash",
        search_model_name: "gpt-4.1,claude-sonnet-4,gemini-2.5-flash",
        icon: "r", // 需要根据实际图标变量替换
        label: "Mixture-of-Agents",
        full_label: "Mixture-of-Agents",
        description: "Auto mixes best AI models for your task", // 需要根据实际i18n函数替换
        support_images: true,
        support_files: true
    },
    {
        name: "gpt-4.1",
        icon: "e", // 需要根据实际图标变量替换
        label: "GPT-4.1",
        full_label: "Open AI GPT-4.1",
        support_images: true,
        support_files: true
    },
    {
        name: "o3",
        icon: "e", // 需要根据实际图标变量替换
        label: "o3",
        full_label: "Open AI o3",
        support_images: true,
        support_files: true,
        hidden: true
    },
    {
        name: "o3-pro",
        icon: "e", // 需要根据实际图标变量替换
        label: "o3-pro",
        full_label: "Open AI o3-pro",
        support_images: true,
        support_files: true,
        disable_search_web: true
    },
    {
        name: "o4-mini-high",
        icon: "e", // 需要根据实际图标变量替换
        label: "o4-mini-high",
        full_label: "Open AI o4-mini-high",
        support_images: false,
        support_files: true
    },
    {
        name: "claude-3-7-sonnet-thinking",
        icon: "t", // 需要根据实际图标变量替换
        label: "Claude Sonnet 3.7 (Thinking)",
        full_label: "Anthropic Claude Sonnet 3.7 (Thinking)",
        support_images: true,
        support_files: true,
        hidden: true
    },
    {
        name: "claude-3-7-sonnet",
        icon: "t", // 需要根据实际图标变量替换
        label: "Claude Sonnet 3.7",
        full_label: "Anthropic Claude 3.7 Sonnet",
        support_images: true,
        support_files: true,
        hidden: true
    },
    {
        name: "claude-sonnet-4-thinking",
        icon: "t", // 需要根据实际图标变量替换
        label: "Claude Sonnet 4 (Thinking)",
        full_label: "Anthropic Claude Sonnet 4 (Thinking)",
        support_images: true,
        support_files: true,
        hidden: true
    },
    {
        name: "claude-sonnet-4",
        icon: "t", // 需要根据实际图标变量替换
        label: "Claude Sonnet 4",
        full_label: "Anthropic Claude Sonnet 4",
        support_images: true,
        support_files: true
    },
    {
        name: "claude-opus-4",
        icon: "t", // 需要根据实际图标变量替换
        label: "Claude Opus 4",
        full_label: "Anthropic Claude Opus 4",
        support_images: true,
        support_files: true
    },
    {
        name: "gemini-2.5-flash",
        icon: "a", // 需要根据实际图标变量替换
        label: "Gemini 2.5 Flash",
        full_label: "Google Gemini 2.5 Flash",
        support_images: true,
        support_files: true
    },
    {
        name: "gemini-2.5-pro",
        icon: "a", // 需要根据实际图标变量替换
        label: "Gemini 2.5 Pro",
        full_label: "Google Gemini 2.5 Pro",
        support_images: true,
        support_files: true
    },
    {
        name: "deep-seek-v3",
        icon: "ee", // 需要根据实际图标变量替换
        label: "DeepSeek V3",
        full_label: "DeepSeek V3",
        support_images: false,
        support_files: true,
        hidden: true
    },
    {
        name: "deep-seek-r1",
        icon: "ee", // 需要根据实际图标变量替换
        label: "DeepSeek R1",
        full_label: "DeepSeek R1",
        support_images: false,
        support_files: true
    },
    {
        name: "kimi-k2-instruct",
        icon: "ne", // 需要根据实际图标变量替换
        label: "Kimi K2 Instruct",
        full_label: "Kimi K2 Instruct",
        support_images: false,
        support_files: true,
        hidden: true
    },
    {
        name: "groq-kimi-k2-instruct",
        icon: "ne", // 需要根据实际图标变量替换
        label: "Groq Kimi K2 Instruct",
        full_label: "Groq Kimi K2 Instruct",
        support_images: false,
        support_files: true,
        hidden: true
    },
    {
        name: "grok-4-0709",
        icon: "ie", // 需要根据实际图标变量替换，原代码中是f(ie)
        label: "Grok4 0709",
        full_label: "Grok4 0709",
        support_images: false,
        support_files: true
    }
];

/**
 * 根据模型名称获取模型UI配置
 * @param {string} modelName - 模型名称
 * @returns {object|null} 模型UI配置对象，如果未找到则返回null
 */
export function getModelUIConfig(modelName) {
    return modelUIConfigs.find(config => config.name === modelName) || null;
}

/**
 * 获取所有可见的模型UI配置（排除hidden为true的模型）
 * @returns {Array} 可见的模型UI配置数组
 */
export function getVisibleModelUIConfigs() {
    return modelUIConfigs.filter(config => !config.hidden);
}

/**
 * 根据模型名称获取对应的内部ID
 * @param {string} modelName - 模型名称
 * @returns {string|null} 模型内部ID，如果未找到则返回null
 */
export function getModelId(modelName) {
    return modelsAIds[modelName] || null;
}
