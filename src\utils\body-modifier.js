/**
 * 修改请求body中的模型ID
 * @param {string} bodyStr 原始body字符串
 * @param {string} model 目标模型名称
 * @param {Object} modelsAIds 模型ID映射表
 * @returns {string} 修改后的body字符串
 */
export function modifyBodyForModel(bodyStr, model, modelsAIds) {
  if (!model || !modelsAIds || !modelsAIds[model]) {
    return bodyStr;
  }

  try {
    const body = JSON.parse(bodyStr);
    const targetModelAId = modelsAIds[model];

    // 替换 modelAId
    if (body.modelAId) {
      body.modelAId = targetModelAId;
    }

    // 替换 messages 中 modelId != null 的值
    if (body.messages && Array.isArray(body.messages)) {
      body.messages.forEach(message => {
        if (message.modelId !== null && message.modelId !== undefined) {
          message.modelId = targetModelAId;
        }
      });
    }

    return JSON.stringify(body);
  } catch (err) {
    console.error('[modifyBodyForModel] JSON解析失败:', err);
    return bodyStr;
  }
}
